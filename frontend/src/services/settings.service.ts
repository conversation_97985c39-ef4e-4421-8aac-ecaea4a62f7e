import axiosInstance from '@/lib/axios'
import { GetAvailableModelsResponse, IModel } from '@/typings/settings'

class SettingsService {
    async createModel(payload: IModel): Promise<IModel> {
        const response = await axiosInstance.post<IModel>(
            '/user-settings/models',
            payload
        )
        return response.data
    }

    async updateModel(id: string, payload: Partial<IModel>): Promise<IModel> {
        const response = await axiosInstance.put<IModel>(
            `/user-settings/models/${id}`,
            payload
        )
        return response.data
    }

    async deleteModel(id: string): Promise<void> {
        await axiosInstance.delete(`/user-settings/models/${id}`)
    }

    async getAvailableModels(): Promise<GetAvailableModelsResponse> {
        const response = await axiosInstance.get<GetAvailableModelsResponse>(
            `/user-settings/models`
        )
        return response.data
    }

    async getModelById(id: string): Promise<IModel> {
        const response = await axiosInstance.get<IModel>(
            `/user-settings/models/${id}`
        )
        return response.data
    }
}

export const settingsService = new SettingsService()
