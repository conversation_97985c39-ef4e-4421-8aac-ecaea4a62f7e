import { createSlice, PayloadAction } from '@reduxjs/toolkit'

interface UploadedFile {
    id: string
    name: string
    path: string
    size: number
}

interface FilesState {
    isUploading: boolean
    uploadedFiles: UploadedFile[]
    filesContent: { [key: string]: string }
    requireClearFiles: boolean
}

const initialState: FilesState = {
    isUploading: false,
    uploadedFiles: [],
    filesContent: {},
    requireClearFiles: false
}

const filesSlice = createSlice({
    name: 'files',
    initialState,
    reducers: {
        setIsUploading: (state, action: PayloadAction<boolean>) => {
            state.isUploading = action.payload
        },
        setUploadedFiles: (state, action: PayloadAction<UploadedFile[]>) => {
            state.uploadedFiles = action.payload
        },
        addUploadedFiles: (state, action: PayloadAction<UploadedFile[]>) => {
            state.uploadedFiles.push(...action.payload)
        },
        setFilesContent: (
            state,
            action: PayloadAction<{ [key: string]: string }>
        ) => {
            state.filesContent = action.payload
        },
        addFileContent: (
            state,
            action: PayloadAction<{ path: string; content: string }>
        ) => {
            state.filesContent[action.payload.path] = action.payload.content
        },
        removeUploadedFile: (state, action: PayloadAction<string>) => {
            state.uploadedFiles = state.uploadedFiles.filter(
                (file) => file.path !== action.payload
            )
            delete state.filesContent[action.payload]
        },
        setRequireClearFiles: (state, action: PayloadAction<boolean>) => {
            state.requireClearFiles = action.payload
        }
    }
})

export const {
    setIsUploading,
    setUploadedFiles,
    addUploadedFiles,
    setFilesContent,
    addFileContent,
    removeUploadedFile,
    setRequireClearFiles
} = filesSlice.actions
export const filesReducer = filesSlice.reducer

// Selectors
export const selectIsUploading = (state: { files: FilesState }) =>
    state.files.isUploading
export const selectUploadedFiles = (state: { files: FilesState }) =>
    state.files.uploadedFiles
export const selectUploadedFilePaths = (state: { files: FilesState }) =>
    state.files.uploadedFiles.map((file) => file.path)
export const selectUploadedFileIds = (state: { files: FilesState }) =>
    state.files.uploadedFiles.map((file) => file.id)
export const selectFilesContent = (state: { files: FilesState }) =>
    state.files.filesContent
export const selectRequireClearFiles = (state: { files: FilesState }) =>
    state.files.requireClearFiles
