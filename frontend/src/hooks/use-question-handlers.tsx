import { useMemo } from 'react'
import { toast } from 'sonner'

import {
    addMessage,
    selectAvailableModels,
    selectCurrentQuestion,
    selectIsAgentInitialized,
    selectIsLoading,
    selectMessages,
    selectSelectedModel,
    selectToolSettings,
    selectUploadedFileIds,
    selectWsConnectionState,
    setBuildStep,
    setCompleted,
    setCurrentActionData,
    setCurrentQuestion,
    setGeneratingPrompt,
    setIsCreatingSession,
    setLoading,
    setMessages,
    setPlans,
    setRequireClearFiles,
    setStopped,
    updateMessage,
    useAppDispatch,
    useAppSelector
} from '@/state'
import { WebSocketConnectionState } from '@/typings/agent'
import { BUILD_STEP, Message } from '@/typings/agent'
import { useSocketIOContext } from '@/contexts/websocket-context'

export function useQuestionHandlers() {
    const dispatch = useAppDispatch()
    const { sendMessage, joinSession } = useSocketIOContext()

    const isLoading = useAppSelector(selectIsLoading)
    const messages = useAppSelector(selectMessages)
    const isAgentInitialized = useAppSelector(selectIsAgentInitialized)
    const selectedModelId = useAppSelector(selectSelectedModel)
    const availableModels = useAppSelector(selectAvailableModels)
    const toolSettings = useAppSelector(selectToolSettings)
    const uploadedFileIds = useAppSelector(selectUploadedFileIds)
    const currentQuestion = useAppSelector(selectCurrentQuestion)
    const wsConnectionState = useAppSelector(selectWsConnectionState)

    const selectedModel = useMemo(
        () => availableModels.find((m) => m.id === selectedModelId),
        [selectedModelId, availableModels]
    )

    const handleEnhancePrompt = () => {
        if (wsConnectionState !== WebSocketConnectionState.CONNECTED) {
            toast.error('WebSocket connection is not open. Please try again.')
            return
        }
        dispatch(setGeneratingPrompt(true))
        sendMessage({
            type: 'enhance_prompt',
            content: {
                model_name: selectedModel?.model,
                text: currentQuestion,
                files: uploadedFileIds,
                tool_args: {
                    thinking_tokens: 0
                }
            }
        })
    }

    const handleQuestionSubmit = async (
        newQuestion: string,
        isNewSession = false
    ) => {
        if (!newQuestion.trim() || isLoading) return

        if (wsConnectionState !== WebSocketConnectionState.CONNECTED) {
            toast.error('WebSocket connection is not open. Please try again.')
            dispatch(setLoading(false))
            return
        }

        dispatch(setLoading(true))
        dispatch(setCompleted(false))
        dispatch(setStopped(false))
        dispatch(setPlans([]))
        dispatch(setBuildStep(BUILD_STEP.THINKING))
        dispatch(setCurrentActionData(undefined))

        // Show all hidden messages
        messages.forEach((message) => {
            if (message.isHidden) {
                dispatch(updateMessage({ ...message, isHidden: false }))
            }
        })

        const newUserMessage: Message = {
            id: Date.now().toString(),
            role: 'user',
            content: newQuestion,
            timestamp: Date.now()
        }

        if (isNewSession) {
            dispatch(setMessages([newUserMessage]))
            dispatch(setIsCreatingSession(true))
        } else {
            dispatch(setRequireClearFiles(true))
            dispatch(setCurrentQuestion(''))
            dispatch(addMessage(newUserMessage))
        }

        const { thinking_tokens, ...tool_args } = toolSettings

        // Initialize session before sending any messages

        // Only send init_agent event if agent is not already initialized
        console.log('isAgentInitialized', isAgentInitialized)
        if (!isAgentInitialized) {
            joinSession()
            sendMessage({
                type: 'init_agent',
                content: {
                        model_id: selectedModel?.id,
                        provider: selectedModel?.api_type,
                        source: selectedModel?.source,
                        tool_args,
                        thinking_tokens
                }
            })
        }

        // Send the query using the existing socket connection
        sendMessage({
            type: 'query',
            content: {
                text: newQuestion,
                resume: messages.length > 0,
                files: uploadedFileIds
            }
        })
    }

    const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault()
            handleQuestionSubmit((e.target as HTMLTextAreaElement).value)
        }
    }

    return {
        handleEnhancePrompt,
        handleQuestionSubmit,
        handleKeyDown
    }
}
