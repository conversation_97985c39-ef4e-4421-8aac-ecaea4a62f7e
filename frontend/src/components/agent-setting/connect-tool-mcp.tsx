import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'

import { Icon } from '../ui/icon'
import { Sheet, SheetClose, Sheet<PERSON>ontent, Sheet<PERSON>eader } from '../ui/sheet'
import { Form, FormControl, FormField, FormItem, FormLabel } from '../ui/form'
import { Textarea } from '../ui/textarea'
import { Button } from '../ui/button'

const FormSchema = z.object({
    tool_config: z.string()
})

interface ConnectToolMCPProps {
    open: boolean
    onOpenChange: (open: boolean) => void
}

const ConnectToolMCP = ({ open, onOpenChange }: ConnectToolMCPProps) => {
    const form = useForm<z.infer<typeof FormSchema>>({
        resolver: zodResolver(FormSchema),
        defaultValues: {
            tool_config: ''
        }
    })

    const handleTestConnection = () => {
        // Test connection
    }

    const onSubmit = (data: z.infer<typeof FormSchema>) => {
        console.log(data)
    }

    return (
        <Sheet open={open} onOpenChange={onOpenChange}>
            <SheetContent className="px-6 py-12 w-full !max-w-[560px]">
                <SheetHeader className="p-0 gap-6 pb-4">
                    <div className="flex items-center justify-between">
                        <p className="text-2xl font-bold">Connect new tool</p>
                        <div className="flex items-center gap-x-4">
                            <SheetClose className="cursor-pointer">
                                <Icon
                                    name="arrow-right"
                                    className="dark:inline hidden"
                                />
                                <Icon
                                    name="arrow-right-dark"
                                    className="dark:hidden inline"
                                />
                            </SheetClose>
                        </div>
                    </div>
                </SheetHeader>
                <Form {...form}>
                    <form
                        onSubmit={form.handleSubmit(onSubmit)}
                        className="space-y-6 overflow-auto pb-12"
                    >
                        <FormField
                            control={form.control}
                            name="tool_config"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel className="text-lg">
                                        MCP Connect JSON
                                    </FormLabel>
                                    <FormControl>
                                        <div className="space-y-2 relative">
                                            <Icon
                                                name="key-square"
                                                className={`absolute top-3 left-4 ${field.value ? '' : 'opacity-30'}`}
                                            />
                                            <Textarea
                                                id="tool-config"
                                                className="pl-[56px] min-h-[144px] mb-4"
                                                placeholder="Enter MCP Connect JSON"
                                                {...field}
                                            />
                                            <Button
                                                type="button"
                                                className="h-12 rounded-xl bg-green text-base font-bold px-11"
                                                onClick={handleTestConnection}
                                            >
                                                Test Connection
                                            </Button>
                                        </div>
                                    </FormControl>
                                </FormItem>
                            )}
                        />
                        <div className="space-y-4 grid grid-cols-2 gap-4">
                            <Button
                                type="reset"
                                variant="outline"
                                className="h-12 rounded-xl text-base"
                                onClick={() => onOpenChange(false)}
                            >
                                Cancel
                            </Button>
                            <Button
                                type="submit"
                                className="h-12 rounded-xl bg-sky-blue text-black text-base"
                            >
                                Connect
                            </Button>
                        </div>
                    </form>
                </Form>
            </SheetContent>
        </Sheet>
    )
}

export default ConnectToolMCP
