from abc import ABC, abstractmethod
from typing import BinaryIO

# Abstract Object Storage Interface
class ObjectStorage(ABC):
    @abstractmethod
    def upload_file(self, source_file_content: BinaryIO, destination_blob_name: str):
        pass

    @abstractmethod
    def get_file(self, source_blob_name: str) -> BinaryIO:
        pass

    @abstractmethod
    def get_download_signed_url(self, source_blob_name: str, expiration_seconds: int) -> str:
        pass

    @abstractmethod
    def get_upload_signed_url(self, destination_blob_name: str, content_type: str, expiration_seconds: int) -> str:
        pass

    @abstractmethod
    def is_exists(self, blob_name: str) -> bool:
        pass

    @abstractmethod
    def get_file_size(self, blob_name: str) -> int:
        pass

    @abstractmethod
    def get_public_url(self, blob_name: str) -> str:
        pass