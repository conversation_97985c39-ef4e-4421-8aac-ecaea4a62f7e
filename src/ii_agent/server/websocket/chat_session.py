import asyncio
import json
import logging
import uuid
from typing import Optional, TYPE_CHECKING, List

import socketio
from pydantic import ValidationError

from ii_agent.controller.agent_controller import Agent<PERSON><PERSON>roller
from ii_agent.core.event import RealtimeEvent, EventType
from ii_agent.core.event_stream import AsyncEventStream
from ii_agent.core.config.ii_agent_config import IIAgentConfig
from ii_agent.core.storage.files import FileStore
from ii_agent.server.models.messages import WebSocketMessage
from ii_agent.subscribers.socketio_subscriber import SocketIOSubscriber
from ii_agent.subscribers.database_subscriber import DatabaseSubscriber
from ii_agent.utils.workspace_manager import WorkspaceManager
from ii_agent.db.manager import Sessions
from ii_tool.sandbox.providers.base import Sandbox

if TYPE_CHECKING:
    from ii_agent.server.services.agent_service import AgentService
    from ii_agent.server.services.message_service import MessageService
    from ii_agent.server.services.sandbox_service import SandboxService

logger = logging.getLogger(__name__)


class ChatSession:
    """Manages a single standalone chat session with its own agent, workspace, and message handling."""

    def __init__(
        self,
        sockets: List[str],
        workspace_manager: WorkspaceManager,
        session_uuid: uuid.UUID,
        agent_service: "AgentService",
        message_service: "MessageService",
        sandbox_service: "SandboxService",
        file_store: FileStore,
        config: IIAgentConfig,
        sio: socketio.AsyncServer,
        user_id: Optional[str] = None,
    ):
        self.sockets = sockets
        self.workspace_manager = workspace_manager
        self.session_uuid = session_uuid
        self.agent_service = agent_service
        self.sandbox_service = sandbox_service
        self.message_service = message_service
        self.file_store = file_store
        self.config = config
        self.sio = sio
        self.user_id = user_id

        # Create event stream for this session
        self.event_stream = AsyncEventStream(logger=logger)

        # Note: Session existence will be ensured in start_chat_loop
        # since __init__ cannot be async

        # Set up subscribers
        self._setup_subscribers()

        # Session state
        self.agent_controller: Optional[AgentController] = None
        self.reviewer_controller: Optional[AgentController] = None
        self.active_task: Optional[asyncio.Task] = None
        self.first_message = True
        self.enable_reviewer = False
        self.session_created = False
        self.sandbox: Optional[Sandbox] = None
        self.chat_loop_lock = asyncio.Lock()
        self.chat_loop_running = False
        self.disconnect_wait_lock = asyncio.Lock()
    
    def get_sandbox(self) -> Sandbox:
        if not self.sandbox:
            raise ValueError("Sandbox not initialized")
        return self.sandbox
    
    async def write_credential(self):
        """Install dependencies for the sandbox."""
        if not self.sandbox:
            raise ValueError("Sandbox not initialized")
        await self.sandbox.write_file(
            file_content=str(self.session_uuid),
            file_path="credential.json"
        )
    
    async def _ensure_sandbox(self):
        """Ensure a sandbox exists for the given session ID."""
        try:
            existing_session = await Sessions.get_session_by_id(self.session_uuid)
            if not existing_session:
                raise Exception(f"Session {self.session_uuid} not found")
            if not await Sessions.session_has_sandbox(self.session_uuid):
                new_sandbox_id = str(uuid.uuid4())
                self.sandbox = await self.sandbox_service.create_sandbox(new_sandbox_id, "user_id")
                await Sessions.update_sandbox_id(
                    session_uuid=self.session_uuid,
                    sandbox_id=new_sandbox_id
                )
                logger.info(
                    f"Created new session {self.session_uuid} with sandbox {new_sandbox_id}"
                )
                await self.write_credential()
            else:
                self.sandbox = await self.sandbox_service.connect_or_resume_sandbox(
                    str(existing_session.sandbox_id)
                )
        except Exception as e:
            raise Exception(f"Failed to ensure sandbox: {e}")

    async def _ensure_session_exists(self):
        """Ensure a database session exists for the given session ID."""
        existing_session = await Sessions.get_session_by_id(self.session_uuid)
        if existing_session:
            logger.info(
                f"Found existing session {self.session_uuid} for user {existing_session.user_id}"
            )
            # Update the user_id if not set
            if not self.user_id:
                self.user_id = existing_session.user_id
        else:
            # Create new session if it doesn't exist
            # Require user_id since authentication is mandatory
            if not self.user_id:
                raise ValueError("Cannot create session without authenticated user_id")

            await Sessions.create_session(
                session_uuid=self.session_uuid,
                user_id=self.user_id,
                name=None,  # Title will be set later when first message is received
            )
            logger.info(
                f"Created new session {self.session_uuid} for user {self.user_id} with workspace at {self.workspace_manager.root}"
            )

    def _setup_subscribers(self):
        """Set up event stream subscribers for Socket.IO and database."""
        # Add Socket.IO subscriber
        self.socketio_subscriber = SocketIOSubscriber(self.sio, str(self.session_uuid))
        self.event_stream.subscribe(self.socketio_subscriber.handle_event)

        # Add database subscriber
        self.database_subscriber = DatabaseSubscriber(self.session_uuid)
        self.event_stream.subscribe(self.database_subscriber.handle_event)

    def get_event_stream(self) -> AsyncEventStream:
        """Get the event stream for this session."""
        return self.event_stream

    def save_session_state(self):
        """Save the current session state to file store."""
        try:
            if (
                self.agent_controller
                and hasattr(self.agent_controller, "state")
                and self.agent_controller.state
            ):
                self.agent_controller.state.save_to_session(
                    str(self.session_uuid), self.file_store
                )
                logger.info(f"Saved session state for session {self.session_uuid}")
            else:
                logger.debug(f"No agent state to save for session {self.session_uuid}")
        except Exception as e:
            logger.error(f"Error saving session state for {self.session_uuid}: {e}")

    def add_socket(self, sid: str):
        """Add a new socket to the broadcast list."""
        if sid not in self.sockets:
            self.sockets.append(sid)
    
    def remove_socket(self, sid: str):
        """Remove a socket from the broadcast list."""
        if sid in self.sockets:
            self.sockets.remove(sid)

    async def send_event(self, event: RealtimeEvent):
        """Send an event to all clients connected to this session via the event stream."""
        # Use the event stream instead of direct Socket.IO sends to avoid race conditions
        # The SocketIOSubscriber will handle the actual sending to the room
        self.event_stream.add_event(event)

    async def handle_socket_message(self, sid: str, message_data: dict):
        """Handle incoming Socket.IO messages for this session."""
        try:
            # Try to acquire processing rights
            async with self.chat_loop_lock:
                if not self.has_active_task():
                    logger.info(f"Processing message from Socket.IO client {sid} for session {self.session_uuid}")
                    try:
                        await self.handle_message(message_data)
                    except Exception as e:
                        logger.error(f"Error handling message: {e}")
                        await self.send_event(
                            RealtimeEvent(
                                type=EventType.ERROR,
                                content={"message": f"Error processing request: {str(e)}"},
                            )
                        )
                # If chat loop is running, ignore the message (another socket is processing)
        except Exception as e:
            logger.error(f"Error in message handler for socket {sid}: {e}")

    async def handshake(self):
        """Handle handshake message."""
        await self.send_event(
            RealtimeEvent(
                type=EventType.CONNECTION_ESTABLISHED,
                content={
                    "message": "Connected to Agent WebSocket Server",
                    "workspace_path": str(self.workspace_manager.root),
                },
            )
        )

    async def handle_message(self, message_data: dict):
        """Handle incoming WebSocket messages for this session."""
        try:
            # Validate message structure
            ws_message = WebSocketMessage(**message_data)
            msg_type = ws_message.type
            content = ws_message.content

            # Delegate to message service
            await self.message_service.process_message(msg_type, content, self)

        except ValidationError as e:
            await self.send_event(
                RealtimeEvent(
                    type=EventType.ERROR,
                    content={"message": f"Invalid message format: {str(e)}"},
                )
            )
        except Exception as e:
            logger.error(f"Error handling message: {str(e)}")
            await self.send_event(
                RealtimeEvent(
                    type=EventType.ERROR,
                    content={"message": f"Error processing request: {str(e)}"},
                )
            )

    async def handshake(self):
        """Handle handshake message."""
        await self.send_event(
            RealtimeEvent(
                type=EventType.CONNECTION_ESTABLISHED,
                content={
                    "message": "Connected to Agent Socket.IO Server",
                    "workspace_path": str(self.workspace_manager.root),
                },
            )
        )

    def has_active_task(self) -> bool:
        """Check if there's an active task for this session."""
        return self.active_task is not None and not self.active_task.done()
    
    async def cleanup_sandbox(self):
        try: 
            await self._ensure_sandbox()
            await self.sandbox_service.schedule_timeout(self.get_sandbox().sandbox_id, 60 * 15)
        except Exception as e:
            logger.error(f"Error cleaning up sandbox: {str(e)}")

    async def cleanup(self):
        """Clean up resources associated with this session."""
        if self.agent_controller:
            self.agent_controller.cancel()

        # Cancel any running tasks
        if self.active_task and not self.active_task.done():
            self.active_task.cancel()
            self.active_task = None

        # Clean up event stream subscribers
        if hasattr(self, "socketio_subscriber"):
            self.event_stream.unsubscribe(self.socketio_subscriber.handle_event)
        if hasattr(self, "database_subscriber"):
            self.event_stream.unsubscribe(self.database_subscriber.handle_event)

        # Clean up references
        self.sockets.clear()
        self.agent_controller = None
        self.reviewer_controller = None
        self.event_stream = None
